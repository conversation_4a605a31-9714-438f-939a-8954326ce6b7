<script setup>
import { ref, watch, computed, provide, nextTick, onUnmounted } from "vue"
import {
  onLoad,
  onShow,
  onShareAppMessage,
  onShareTimeline,
} from "@dcloudio/uni-app"
import { sleep } from "/utils"
import { useStore } from "/store"
import request from "/request"
import MySocket from "./MySocket"
import { useTTS } from "./tts"
import { useST } from "./st"

const store = useStore()

// 语音相关状态
const tts = ref(null)
const st = ref(null)
const isMute = ref(false)

// 消息相关状态
const isGenerating = ref(false)
const messagesList = ref([])
const chatMode = ref("assistant")

// 语音队列相关
let msgContentQueue = []
let speakFlag = false
let isProcessingQueue = false // 防止队列处理重复执行

// 弹窗状态
const rechargePopupVisible = ref(false)
const feedBackVisible = ref(false)
const serviceVisible = ref(false)
const completeInfoPopupVisible = ref(false)
const callVisible = ref(false)
const callStatus = ref("")

// 评价相关
const toRateAnswerInstance = ref(null)
const defaultRateAnswer = ref(null)

// 计算属性
const headerHeight = computed(() => store.headerHeight)
const leftTimes = computed(() => store.leftTimes)

// 消息处理 - 优化后的实时朗读队列处理
const handleSpeakRealTime = async () => {
  if (isProcessingQueue) {
    console.log("[RealTime] 队列处理中，跳过重复调用")
    return
  }

  isProcessingQueue = true

  try {
    while (msgContentQueue.length > 0 || !isGenerationComplete()) {
      // 如果队列为空但生成未完成，等待新内容
      if (msgContentQueue.length === 0) {
        if (!isGenerationComplete()) {
          console.log("[RealTime] 等待新内容...")
          await sleep(200) // 减少等待时间，提高响应性
          continue
        } else {
          // 生成完成且队列为空，结束处理
          console.log("[RealTime] 生成完成，队列处理结束")
          break
        }
      }

      // 处理队列中的文本
      const text = msgContentQueue.shift()
      if (text && text.trim()) {
        console.log(`[RealTime] 发送文本: ${text.substring(0, 50)}...`)
        try {
          await tts.value?.sendTTSText(text)
          await sleep(50) // 减少间隔时间
        } catch (error) {
          console.error("[RealTime] 发送TTS文本失败:", error)
          // 发送失败时，可以选择重新加入队列或跳过
          break
        }
      }
    }
  } catch (error) {
    console.error("[RealTime] 队列处理出错:", error)
  } finally {
    // 重置状态
    isProcessingQueue = false
    speakFlag = false

    // 如果生成完成，停止TTS（但不停止音频播放）
    if (isGenerationComplete()) {
      console.log("[RealTime] 队列处理完成，停止TTS连接")
      tts.value?.stopTTS(false)
    }
  }
}

// 检查生成是否完成
const isGenerationComplete = () => {
  const lastMessage = messagesList.value[messagesList.value.length - 1]
  return lastMessage?.status === "done"
}

// 语音监听 - 增强错误处理
const listenTTS = () => {
  tts.value?.onTTSChange(async (name, msg) => {
    console.log(`[TTS Event] ${name}:`, msg)

    switch (name) {
      case "failed":
        console.error("[TTS] TTS失败:", msg)
        handleTTSError(msg)
        break

      case "started":
        console.log("[TTS] TTS已启动")
        break

      case "completed":
        console.log("[TTS] TTS完成")
        break

      case "closed":
        console.log("[TTS] TTS连接已关闭")
        break

      default:
        console.log(`[TTS] 未处理的事件: ${name}`)
    }
  })
}

// TTS错误处理
const handleTTSError = async (errorMsg) => {
  console.error("[TTS Error] 处理TTS错误:", errorMsg)

  // 清理状态
  msgContentQueue = []
  speakFlag = false
  isProcessingQueue = false

  // 如果在通话中，重启语音识别
  if (callVisible.value) {
    callStatus.value = ""
    try {
      await st.value?.stopST()
      await sleep(1000) // 等待一秒后重启
      st.value?.startST()
    } catch (error) {
      console.error("[TTS Error] 重启语音识别失败:", error)
    }
  }

  // 显示错误提示
  uni.showToast({
    title: "语音播放出现问题，请重试",
    icon: "none",
    duration: 2000,
  })
}

const listenSt = () => {
  st.value?.onStChange(async (name, msg) => {
    console.log("st.value?.onStChange", name, msg)

    switch (name) {
      case "started":
        callStatus.value = "listen"
        uni.hideLoading({ noConflict: true })
        break

      case "end":
        st.value?.stopST()
        const text = msg.payload.result
        if (text) {
          onConfirm(text)
            .then(() => {
              callStatus.value = "think"
            })
            .catch(() => {
              setTimeout(() => {
                st.value?.startST()
              }, 1000)
            })
        }
        break

      case "failed":
        if (callStatus.value === "listen") {
          callStatus.value = ""
          await st.value?.stopST()
          st.value?.startST()
        }
        break
    }
  })
}

// Socket消息处理
const onMessage = async (res) => {
  try {
    const { action, data } = res.result

    if (action === "reply") {
      if (data.session_id) {
        store.setSessionId(data.session_id)
      }

      const lastMessage = messagesList.value[messagesList.value.length - 1]

      if (data.finish_reason !== "stop") {
        if (chatMode.value === "artificial") {
          messagesList.value.push({
            role: "artificial",
            content: data.content,
            status: "done",
          })
        } else if (chatMode.value === "assistant") {
          if (lastMessage.status === "done") {
            return
          }
          lastMessage.status = "output"
          lastMessage.content += data.content
        }

        // 优化朗读触发逻辑
        if ((callVisible.value || !isMute.value) && data.content.trim()) {
          msgContentQueue.push(data.content)
          console.log(
            `[Socket] 添加到朗读队列: ${data.content.substring(0, 30)}...`
          )

          if (!speakFlag && !isProcessingQueue) {
            speakFlag = true
            console.log("[Socket] 启动TTS和队列处理")

            try {
              await tts.value?.startTTS()
              await sleep(300) // 减少等待时间
              handleSpeakRealTime()
            } catch (error) {
              console.error("[Socket] 启动TTS失败:", error)
              speakFlag = false
            }
          }
        }
      } else {
        store.minusLeftTimes()
        isGenerating.value = false
        lastMessage.status = "done"
        lastMessage.record_id = data.record_id
      }
    }
  } catch (error) {
    console.error("Error in onMessage:", error)
  }
}

const mySocket = new MySocket({ onMessage })

// 消息发送
const onConfirm = (text) => {
  if (isGenerating.value) {
    abort()
  } else {
    tts.value?.stopTTS()
  }

  return new Promise((resolve, reject) => {
    if (!mySocket?.isConnecting) {
      reject("socket is not connecting")
    }

    mySocket
      .send({
        action: "question",
        prompt: text,
      })
      .then(() => {
        resolve()
        messagesList.value.push({
          role: "user",
          content: text,
        })

        if (chatMode.value === "assistant") {
          messagesList.value.push({
            role: "assistant",
            content: "",
            status: "generating",
          })
          isGenerating.value = true

          if (callVisible.value || !isMute.value) {
            const msg = messagesList.value[messagesList.value.length - 1]
            msg.voiceStatus = "loading"

            tts.value?.onVoiceStatusChange((e) => {
              console.log("onVoiceStatusChange", e)
              handleVoiceStatusChange(e, msg)
            })
          }
        }

        // 重置队列状态
        msgContentQueue = []
        speakFlag = false
        isProcessingQueue = false
        console.log("[Confirm] 重置朗读队列状态")
      })
      .catch(async (err) => {
        reject(err)
        console.error("onConfirm", err)
        uni.showToast({
          title: "发送失败, 请重试",
          icon: "none",
        })
        if (callVisible.value) {
          callStatus.value = ""
          await st.value?.stopST()
          st.value?.startST()
        }
      })
  })
}

// 语音状态变化处理 - 优化状态同步
const handleVoiceStatusChange = (status, msg) => {
  console.log(
    `[VoiceStatus] 状态变化: ${status}, 消息ID: ${msg.record_id || "unknown"}`
  )

  // 确保消息对象存在
  if (!msg) {
    console.warn("[VoiceStatus] 消息对象为空")
    return
  }

  const previousStatus = msg.voiceStatus

  switch (status) {
    case "play":
      msg.voiceStatus = "active"
      if (callVisible.value) {
        callStatus.value = "speak"
      }
      console.log(`[VoiceStatus] 开始播放: ${previousStatus} -> active`)
      break

    case "pause":
      msg.voiceStatus = "pause"
      console.log(`[VoiceStatus] 暂停播放: ${previousStatus} -> pause`)
      break

    case "stop":
      msg.voiceStatus = "inactive"
      console.log(`[VoiceStatus] 停止播放: ${previousStatus} -> inactive`)
      break

    case "complete":
      msg.voiceStatus = "inactive"
      console.log(`[VoiceStatus] 播放完成: ${previousStatus} -> inactive`)

      // 如果在通话中，重启语音识别
      if (callVisible.value) {
        setTimeout(() => {
          st.value?.startST()
        }, 500) // 延迟启动，确保音频播放完全结束
      }
      break

    case "error":
      msg.voiceStatus = "inactive"
      console.error(`[VoiceStatus] 播放错误: ${previousStatus} -> inactive`)

      // 显示错误提示
      uni.showToast({
        title: "播放出现问题",
        icon: "none",
        duration: 1500,
      })

      // 如果在通话中，重启语音识别
      if (callVisible.value) {
        setTimeout(() => {
          st.value?.startST()
        }, 1000)
      }
      break

    case "loading":
      msg.voiceStatus = "loading"
      console.log(`[VoiceStatus] 加载中: ${previousStatus} -> loading`)
      break

    default:
      console.warn(`[VoiceStatus] 未知状态: ${status}`)
      break
  }

  // 触发UI更新
  nextTick(() => {
    console.log(`[VoiceStatus] UI状态已更新: ${msg.voiceStatus}`)
  })
}

// 通话相关
const showCall = () => {
  if (isGenerating.value) {
    abort()
  } else {
    tts.value?.stopTTS()
  }

  uni.showLoading({
    title: "连接中...",
    mask: true,
  })

  callVisible.value = true
  listenSt()
  st.value?.startST()
  wx.setKeepScreenOn({
    keepScreenOn: true,
  })
}

const exitCall = () => {
  console.log("[Call] 退出通话")

  try {
    callVisible.value = false
    callStatus.value = ""
    callStatusBeforePause = ""

    // 关闭屏幕常亮
    wx.setKeepScreenOn({
      keepScreenOn: false,
    })

    // 停止语音识别
    st.value?.stopST()

    // 停止TTS播放
    tts.value?.stopTTS()

    // 清理队列状态
    msgContentQueue = []
    speakFlag = false
    isProcessingQueue = false

    console.log("[Call] 通话已退出，状态已清理")
  } catch (error) {
    console.error("[Call] 退出通话失败:", error)
  }
}

let callStatusBeforePause = ""

const callPause = () => {
  console.log(`[Call] 暂停通话，当前状态: ${callStatus.value}`)

  try {
    if (callStatus.value === "speak") {
      tts.value?.pauseTTS()
      console.log("[Call] 暂停TTS播放")
    } else if (callStatus.value === "think" || isGenerating.value) {
      abort()
      console.log("[Call] 中止生成")
    }

    st.value?.stopST()
    callStatusBeforePause = callStatus.value
    callStatus.value = "pause"

    console.log(`[Call] 通话已暂停，之前状态: ${callStatusBeforePause}`)
  } catch (error) {
    console.error("[Call] 暂停通话失败:", error)
  }
}

const callResume = () => {
  console.log(`[Call] 恢复通话，之前状态: ${callStatusBeforePause}`)

  try {
    if (callStatusBeforePause === "speak") {
      tts.value?.resumeTTS()
      callStatus.value = "speak"
      console.log("[Call] 恢复TTS播放")
    } else {
      st.value?.startST()
      callStatus.value = "listen"
      console.log("[Call] 重启语音识别")
    }
  } catch (error) {
    console.error("[Call] 恢复通话失败:", error)
    // 如果恢复失败，重新开始语音识别
    st.value?.startST()
    callStatus.value = "listen"
  }
}

const callAbort = async () => {
  callStatus.value = ""
  abort()
  await st.value?.stopST()
  st.value?.startST()
}

// 评价相关
const toRateAnswer = async (message) => {
  console.log("toRateAnswer", message)
  toRateAnswerInstance.value = message
  try {
    const res = await request({
      url: `/mini/conversation_record/${message.record_id}/assessment`,
      method: "GET",
    })
    if (res?.code === 200) {
      defaultRateAnswer.value = res.result
    }
    feedBackVisible.value = true
  } catch (error) {
    console.error("Error in toRateAnswer:", error)
  }
}

const onConfirmRate = async (data) => {
  try {
    const res = await request({
      url: `/mini/conversation_record/${toRateAnswerInstance.value.record_id}/assessment`,
      method: "POST",
      data,
    })
    if (res.code === 200) {
      feedBackVisible.value = false
      defaultRateAnswer.value = null
      uni.showToast({
        title: "评价成功",
        icon: "success",
      })
    }
  } catch (error) {
    console.error("Error in onConfirmRate:", error)
  }
}

// 中止生成
const abort = () => {
  request({
    url: "/mini/conversation_stop",
    method: "POST",
    hideLoading: true,
  })
  const lastMessage = messagesList.value[messagesList.value.length - 1]
  if (lastMessage) {
    if (lastMessage.status === "generating") {
      messagesList.value.pop()
    } else if (lastMessage.status === "output") {
      lastMessage.status = "done"
      lastMessage.voiceStatus = "inactive"
    }
  }

  // 清理队列和状态
  msgContentQueue = []
  speakFlag = false
  isProcessingQueue = false

  console.log("[Abort] 停止TTS和清理状态")
  tts.value?.stopTTS()
  isGenerating.value = false
  store.getUserInfo()
}

// 重新生成和重新播放
const reGenerate = () => {
  tts.value?.stopTTS()
  messagesList.value.pop()
  const lastMessage = messagesList.value[messagesList.value.length - 1]
  const text = lastMessage.content
  messagesList.value.pop()
  onConfirm(text)
}

const reSpeak = async (message) => {
  console.log(`[ReSpeak] 重新播放消息，当前状态: ${message.voiceStatus}`)

  try {
    if (message.voiceStatus === "active") {
      // 停止当前播放
      console.log("[ReSpeak] 停止当前播放")
      tts.value?.stopTTS()
      message.voiceStatus = "inactive"
    } else if (!message.voiceStatus || message.voiceStatus === "inactive") {
      // 开始播放
      message.voiceStatus = "loading"
      console.log("[ReSpeak] 开始重新播放")

      // 停止之前的TTS
      await tts.value?.stopTTS()

      // 启动新的TTS会话
      await tts.value?.startTTS()
      await sleep(800) // 等待TTS准备就绪

      // 发送文本
      await tts.value?.sendTTSText(message.content)

      // 监听播放状态变化
      tts.value?.onVoiceStatusChange((status) => {
        console.log(`[ReSpeak] 状态变化: ${status}`)
        switch (status) {
          case "start":
          case "play":
            message.voiceStatus = "active"
            break
          case "pause":
            message.voiceStatus = "pause"
            break
          case "stop":
          case "complete":
            message.voiceStatus = "inactive"
            console.log("[ReSpeak] 播放完成")
            break
          case "error":
            message.voiceStatus = "inactive"
            console.error("[ReSpeak] 播放出错")
            uni.showToast({
              title: "播放失败，请重试",
              icon: "none",
            })
            break
        }
      })
    }
  } catch (error) {
    console.error("[ReSpeak] 重新播放失败:", error)
    message.voiceStatus = "inactive"
    uni.showToast({
      title: "播放失败，请重试",
      icon: "none",
    })
  }
}

// 聊天模式切换
const toChangeChatMode = (mode) => {
  if (mode === chatMode.value) return

  if (mode === "artificial") {
    serviceVisible.value = true
  } else {
    uni.showModal({
      title: "提示",
      content: "是否退出人工服务？",
      success: async (res) => {
        if (res.confirm) {
          try {
            const res1 = await request({
              url: `/mini/conversation_setting`,
              method: "POST",
              data: {
                mode: "assistant",
                type: "consult",
              },
            })
            if (res1.code === 200) {
              chatMode.value = "assistant"
              messagesList.value.push({
                role: "manual",
                content: "",
                status: "exited",
              })
            }
          } catch (error) {
            console.error("Error in toChangeChatMode:", error)
          }
        }
      },
    })
  }
}

const confirmService = async () => {
  try {
    const res = await request({
      url: `/mini/conversation_setting`,
      method: "POST",
      data: {
        mode: "artificial",
        type: "consult",
      },
    })
    if (res.code === 200) {
      chatMode.value = "artificial"
      serviceVisible.value = false
      messagesList.value.push({
        role: "manual",
        content: "",
        status: "entered",
      })
    }
  } catch (error) {
    console.error("Error in confirmService:", error)
  }
}

// 其他功能
const toggleMute = () => {
  isMute.value = !isMute.value
  if (isMute.value) {
    tts.value?.stopTTS()
  }
}

const messageRef = ref(null)
const onInputFocus = () => {
  messageRef.value.scrollIntoBottom()
}
const onInputBlur = () => {
  messageRef.value.updateScrollTop("lower")
}

const checkAvailability = () => {
  if (store.isLogin && !store.isCompleteUserInfo) {
    completeInfoPopupVisible.value = true
  } else if (leftTimes.value <= 0) {
    if (!store.paySwitch) return
    rechargePopupVisible.value = true
  }
}

const goCompleteInfo = () => {
  uni.navigateTo({
    url: "/pages/profile/profile",
  })
  completeInfoPopupVisible.value = false
}

// 初始化 - 增强错误处理
const init = async () => {
  console.log("[Init] 开始初始化应用")

  try {
    // 初始化Socket连接
    console.log("[Init] 初始化Socket连接")
    mySocket.init()

    // 设置对话模式
    console.log("[Init] 设置对话模式")
    const res = await request({
      url: `/mini/conversation_setting`,
      method: "POST",
      data: {
        mode: "assistant",
        type: "consult",
      },
    })

    if (res.code === 200) {
      chatMode.value = "assistant"
      console.log("[Init] 对话模式设置成功")
    } else {
      console.warn("[Init] 对话模式设置失败:", res)
    }

    // 获取NLS Token
    console.log("[Init] 获取NLS Token")
    await store.fetchNlsToken()

    // 初始化TTS和ST
    console.log("[Init] 初始化TTS和ST")
    tts.value = useTTS()
    st.value = useST()

    // 设置TTS监听
    listenTTS()

    console.log("[Init] 应用初始化完成")
  } catch (error) {
    console.error("[Init] 初始化失败:", error)

    // 显示错误提示
    uni.showToast({
      title: "初始化失败，请重试",
      icon: "none",
      duration: 3000,
    })

    // 可以考虑重试机制
    setTimeout(() => {
      console.log("[Init] 尝试重新初始化")
      init()
    }, 3000)
  }
}

// 生命周期
watch(
  () => store.isLogin,
  (value) => {
    if (value) {
      init()
    }
  },
  { immediate: true }
)

onLoad(() => {
  console.log("index onLoad")
})

onShow(() => {
  console.log("index onShow")
})

provide("st", st)

// 分享
onShareAppMessage((res) => {
  return {
    title: store.inviteShareTitle,
    imageUrl: store.inviteShareImage,
    path: `/pages/index/index`,
  }
})

onShareTimeline(() => {})

onUnmounted(() => {
  console.log("[Unmount] 开始清理资源")
  // 清理语音识别资源
  st.value?.cleanup?.()

  // 停止语音合成
  tts.value?.stopTTS()

  // 关闭 socket 连接
  mySocket?.close()

  // 清理队列状态
  msgContentQueue = []
  speakFlag = false
  isProcessingQueue = false

  console.log("[Unmount] 资源清理完成")
})
</script>

<template>
  <image src="/static/images/index-bg.png" class="index-bg" />
  <indexHeader
    :leftTimes="leftTimes"
    :chatMode="chatMode"
    :isMute="isMute"
    @toggleMute="toggleMute"
  />
  <bottomPanel
    :chatMode="chatMode"
    :isGenerating="isGenerating"
    :isMute="isMute"
    :disabled="!store.accountAvailability"
    @toggleMute="toggleMute"
    @confirm="onConfirm"
    @exitArtificial="toChangeChatMode('assistant')"
    @call="showCall"
    @abort="abort"
    @inputFocus="onInputFocus"
    @inputBlur="onInputBlur"
    @click="checkAvailability"
  />

  <view class="index-content" @click="onContentClick">
    <messages
      ref="messageRef"
      v-if="store.isLogin"
      :list="messagesList"
      :isMute="isMute"
      @manual="toChangeChatMode('artificial')"
      @rate="toRateAnswer"
      @refresh="reGenerate"
      @speak="reSpeak"
    />
  </view>

  <call
    :visible="callVisible"
    :callStatus="callStatus"
    @pause="callPause"
    @resume="callResume"
    @abort="callAbort"
    @exit="exitCall"
  />

  <rechargePopup v-model:visible="rechargePopupVisible" />
  <feedBackPopup
    v-model:visible="feedBackVisible"
    :defaultData="defaultRateAnswer"
    @confirm="onConfirmRate"
  />
  <servicePopup v-model:visible="serviceVisible" @confirm="confirmService" />

  <completeInfoPopup
    v-model:visible="completeInfoPopupVisible"
    @confirm="goCompleteInfo"
  />

  <!-- <privacyPopup /> -->
</template>

<style lang="scss" scoped>
.index-bg {
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  z-index: -1;
}

.index-content {
  position: absolute;
  top: v-bind(headerHeight);
  left: 0;
  z-index: 0;
  width: 100vw;
  height: calc(100vh - v-bind(headerHeight));
  padding-bottom: env(safe-area-inset-bottom);
  box-sizing: border-box;
  overflow: hidden;
}
</style>
